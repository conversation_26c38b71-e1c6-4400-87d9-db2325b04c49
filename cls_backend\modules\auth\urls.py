from django.urls import path

from .views import *

urlpatterns = [
    path('login', LoginAPIView.as_view()),
    path('user', RetriveUserAPIView.as_view()),
    path('create-user', CreateUserAPIView.as_view()),
    path('password-reset/', PasswordResetRequestView.as_view(), name='password_reset_request'),
    path('password-reset/<uidb64>/<token>/', PasswordResetConfirmView.as_view(), name='password_reset_confirm'),
    path("password-reset/<uidb64>/<token>/validate", PasswordResetValidateTokenView.as_view(), name="validate-reset-token"),
    path('update-profile', UpdateUserProfileView.as_view({'put': 'update'})),
    path('change-password', PasswordChangeView.as_view({'put': 'update'})),
]
