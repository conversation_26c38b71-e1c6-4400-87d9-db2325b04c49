from .views import *
from rest_framework.routers import DefaultRouter
from django.urls import include, path

router = DefaultRouter(trailing_slash=False)
# router.register(r'files', FindDocumentViewSet, basename='search-documents')
router.register(r'check_exists', CheckDocumentExistsViewSet, basename='search-documents')
router.register(r'histories', FindDocumentHistoryViewSet, basename='search-document-histories')
urlpatterns = [
    path('', include(router.urls)),
    path('compare', DocumentCompareView.as_view()),
    path('by_query_and_specified_docs', DocumentLegalSearchView.as_view()),
    path("<uuid:pk>/update-name/", UpdateDocumentName.as_view(), name="update-document-name"),
    path("extract-document", IEVBHC.as_view(), name='extract-document'),
    path('download', download_file, name='download_file'),
    path('<uuid:pk>/binary/', DocumentBinaryAPIView.as_view(), name='document-binary'),
]
