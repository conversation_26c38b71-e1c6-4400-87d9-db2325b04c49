import time
from decouple import config
import requests
import logging
from celery import shared_task
from cls_backend.models import <PERSON><PERSON><PERSON><PERSON>, ThamQuyenNoiDung
from cls_backend.constants import (
    DIEU_KHOAN_LIEN_QUAN, STATUS_PROCESSING, STATUS_FAILED, STATUS_SUCCESS, STATUS_NOTFOUND, TASK_TIMEOUT, THAM_QUYEN_NOI_DUNG
)
from cls_backend.modules.notification.utils import send_notifications
from cls_backend.modules.notification.event import EVENT_RSDK_DONE

import json
from datetime import datetime
from celery.exceptions import SoftTimeLimitExceeded
from django.db import transaction

from cls_backend.tasks.handle_ie import HARD_TASK_TIMEOUT, SOFT_TASK_TIMEOUT
from cls_backend.utils.timeout_terminate_celery import BaseTask
from cls_backend.utils.handle_file import RedisUtil
logger = logging.getLogger('cls')


@shared_task(autoretry_for=(), max_retries=0, bind=True, base=BaseTask, time_limit=HARD_TASK_TIMEOUT, soft_time_limit=SOFT_TASK_TIMEOUT)
def term_review(self, payload: dict, term_id: int, skip_notif=False):
    """
    Gửi dữ liệu đến server AI để phân tích điều khoản và trả về kết quả.
    :param payload: dict chứa dữ liệu đầu vào.
    :param term_id: ID của điều khoản cần phân tích.
    :param skip_notif: Bỏ qua thông báo hay không.
    :return: dict kết quả từ server AI hoặc lỗi.
    """
    logger.debug('Start term review')
    
    url = config('CLS_HOST') + '/v2/legal_term_analyze'
    headers = {
        "Content-Type": "application/json"
    }
   
    try:
        clause = LawClause.objects.get(id=term_id)
        user_id = clause.document.created_by.id

        if clause.status in [STATUS_SUCCESS]:
            logger.info(f"Điều khoản {term_id} đã được rà soát")
            clause.related_clauses.all().update(deleted_at=datetime.now())

        clause.status = STATUS_PROCESSING
        clause.save(update_fields=['status'])

        payload['text'] = [
            {
                'title': clause.title,
                'content': clause.content
            }
        ]
        logger.debug(payload)
        
        response = requests.post(url, json=payload, headers=headers, timeout=60)
        logger.debug(response)

        # Xử lý response mới trả về session_id
        response_data = json.loads(response.text)
        
        if response_data['status_code'] != 200:
            logger.error(f"Lỗi từ AI server: {response_data}")
            raise Exception(f"Lỗi từ AI server: {response_data['message']}")
            
        session_id = response_data['session_id']
        logger.info(f"Nhận được session_id: {session_id} từ AI server")
        
        # Tạo key Redis để theo dõi kết quả
        result_key = session_id
        
        # Khởi tạo Redis client
        r = RedisUtil.get_instance('celery')
        
        # Chờ kết quả từ Redis với timeout
        time_start = time.time()
        result = None
        
        while time.time() - time_start < SOFT_TASK_TIMEOUT:
            logger.debug(f"Đang chờ kết quả cho session_id {session_id}...")
            
            if r._redis_client.exists(result_key):
                result_data = r._redis_client.get(result_key)
                result = json.loads(result_data)
                logger.info(f"Đã nhận được kết quả từ Redis cho session_id {session_id}")
                break
                
            time.sleep(5)  # Đợi 5 giây trước khi kiểm tra lại
            
        if result is None:
            logger.error(f"Hết thời gian chờ kết quả cho session_id {session_id}")
            raise TimeoutError(f"Hết thời gian chờ kết quả cho điều khoản {term_id}")
            
        # Xử lý kết quả nhận được từ Redis
        clause_result = result['data'][0]
        logger.info("Kết quả từ AI server: %s", clause_result)
        
        # Cập nhật trạng thái và lưu kết quả
        clause.status = STATUS_SUCCESS
        clause.ai_result = clause_result
        clause.save(update_fields=['status', 'ai_result'])

        # Xử lý các điều khoản liên quan
        related_clause_ids = []
        for related_type, related_clauses in [
            (THAM_QUYEN_NOI_DUNG, clause_result['tham_quyen_noi_dung']),
            (DIEU_KHOAN_LIEN_QUAN, clause_result['dieu_khoan_lien_quan'])
        ]:
            for related_clause in related_clauses:
                if related_type == THAM_QUYEN_NOI_DUNG:
                    logger.debug(related_clause)
                    tham_quyen_noi_dung = ThamQuyenNoiDung.objects.create(
                        lawclause=clause,
                        van_de_ban_hanh=related_clause.get('van_de_ban_hanh', None),
                        co_quan_co_tham_quyen=related_clause.get('co_quan_co_tham_quyen', None),
                        ket_qua=related_clause.get('ket_qua', None),
                        ly_do=related_clause.get('ly_do', None)
                    )

                    for sub_law_clause in related_clause.get('quy_dinh_phap_luat_lien_quan', []):
                        obj = LawClause.objects.create(
                            type=related_type,
                            clause_id=sub_law_clause['id'],
                            title=sub_law_clause['title'],
                            law_title=sub_law_clause['title'],
                            content=sub_law_clause['content'],
                            position=sub_law_clause['position'],
                            doc_id=sub_law_clause['id_document'],
                            result=sub_law_clause.get('ket_qua', None),
                            reason=sub_law_clause.get('ly_do', None)
                        )
                        # related_clause_ids.append(obj.id)
                        tham_quyen_noi_dung.related_clauses.add(obj)
                else:
                    obj = LawClause.objects.create(
                        type=related_type,
                        clause_id=related_clause['id'],
                        title=related_clause['title'],
                        law_title=related_clause['title'],
                        content=related_clause['content'],
                        position=related_clause['position'],
                        doc_id=related_clause['id_document'],
                        result=related_clause.get('ket_qua', None),
                        reason=related_clause.get('ly_do', None)
                    )
                    related_clause_ids.append(obj.id)

        clause.related_clauses.add(*related_clause_ids)
        
        if clause.status == STATUS_SUCCESS and not skip_notif:
            send_notifications(
                user_id=user_id, 
                event=EVENT_RSDK_DONE, 
                data={
                    "name": clause.position,
                    "status": STATUS_SUCCESS,
                    "clause_id": clause.id
                }
            )
        return clause_result

    except (requests.exceptions.RequestException, TimeoutError) as e:
        logger.error("Lỗi khi gửi dữ liệu tới AI server hoặc chờ kết quả: %s", str(e))
        logger.exception(e)
        with transaction.atomic():
            clause.status = STATUS_FAILED
            clause.save(update_fields=['status'])
        if not skip_notif:
            send_notifications(
                user_id=user_id, 
                event=EVENT_RSDK_DONE, 
                data={
                    "name": clause.position,
                    "status": STATUS_FAILED,
                    "clause_id": clause.id
                }
            )
    
    except SoftTimeLimitExceeded:
        logger.error(f"Clause {clause.clause_id} exceeded soft time limit. Marking as FAILED.")
        with transaction.atomic():
            clause.status = STATUS_FAILED
            clause.save(update_fields=['status'])
        if not skip_notif:
            send_notifications(
                user_id=user_id, 
                event=EVENT_RSDK_DONE, 
                data={
                    "name": clause.position,
                    "status": STATUS_FAILED,
                    "clause_id": clause.id
                }
            )

    except Exception as e:
        logger.exception(e)
        with transaction.atomic():
            clause.status = STATUS_FAILED
            clause.save(update_fields=['status'])
        if not skip_notif:
            send_notifications(
                user_id=user_id, 
                event=EVENT_RSDK_DONE, 
                data={
                    "name": clause.position,
                    "status": STATUS_FAILED,
                    "clause_id": clause.id
                }
            )
        raise e