import json
from typing import List
from django.db.models import Q
import requests
import unicodedata
from decouple import config
from django.db.models import Count, Avg
from django.db.models.functions import Coalesce
from rest_framework import status
from rest_framework.permissions import IsAuthenticated, AllowAny
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework.viewsets import ViewSet
from cls_backend.models import HistorySearchResult, LegalSearchHistory, User, Document
from cls_backend.modules.keywords.views import LoaiVanBanView
from cls_backend.modules.legal_search.serializers import HistorySearchResultSerializer, LegalSearchHistorySerializer, DocumentSerializer
from cls_backend.utils.es_utils import init_es_client
from collections import Counter
import logging
from cls_backend.utils.file_utils import generate_presigned_url, generate_presigned_url_doc, is_valid_uuid
from rest_framework.decorators import action
from cls_backend.constants import BY_USER, BY_<PERSON>
from cls_backend.modules.ocr.serializers import DocumentRelated
from request_log.decorators import log_requests


logger = logging.getLogger("cls")

class LegalSearchByQueryView(APIView):
    permission_classes = [IsAuthenticated]

    def parse_expression(self, expression: str, operators: List[str]):
        tokens = []
        for char in expression:
            if char in operators:
                tokens.append(char)
            else:
                if tokens and tokens[-1] not in operators:
                    tokens[-1] += char
                else:
                    tokens.append(char)

        # postprocess for whitespace
        tokens = [token.strip() for token in tokens if len(token.strip()) > 0]
        return tokens

    def evaluate_expression(self, expression: str, operators: List[str], query_content: str):
        # 1. Chuẩn hóa Unicode
        expression = unicodedata.normalize("NFC", expression)

        # 2. Parse biểu thức
        tokens = self.parse_expression(expression, operators)

        # 3. Thực hiện biểu thức
        # Do biểu thức là logic nên ta sẽ đưa các token không phải là toán tử
        # vào tìm kiếm trong xâu gốc (query_content) để chuyển thành True hoặc False
        # Tăng tốc độ: Đưa các từ thành dict để lookup nhanh hơn,
        # giảm thiểu việc tìm kiếm trong xâu gốc nhiều lần

        # 3.1. Tạo tập các token không phải là toán tử, aka toán hạng - operand
        operands = set(tokens) - set(operators)

        # 3.2. Tìm kiếm trong xâu gốc và lưu luôn kết quả vào dict
        operand_dict = {operand: str(operand.lower() in query_content.lower()) for operand in operands}

        # 3.3. Fill vào dict các toán tử AND, OR, NOT
        operand_dict['&'] = 'and'
        operand_dict['|'] = 'or'
        operand_dict['^'] = 'not'

        # 3.4. Thay thế các toán hạng bằng True hoặc False
        tokens = [operand_dict.get(token, token) for token in tokens]

        # 3.5. Tính toán biểu thức
        expression = " ".join(tokens)
        return eval(expression)

    def filter_legal_search_data(self, data, expression):
        filtered_data = []
        operators = ['&', '|', '^', '(', ')']
        for obj in data:
            # if obj satisfy filter
            noi_dung = obj['noi_dung']
            valid = self.evaluate_expression(expression, operators, noi_dung)
            if valid:
                filtered_data.append(obj)

        return filtered_data

    @log_requests('legal_search')
    def post(self, request):
        json_data = json.loads(request.body)
        # filter_object = json_data.get("filter", None)
        # if filter_object is None:
        #     del json_data["filter"]
        # payload = json.dumps(json_data)
        # call AI service api
        upload_url = config('CLS_HOST') + config('LEGAL_SEARCH_BY_QUERY')
        try:
            response = requests.get(upload_url, params = json_data)
            response.raise_for_status()  # để ném lỗi nếu status code là 4xx/5xx
            response_json = response.json()  # convert content sang JSON dict
        except Exception as e:
            logger.exception(e)
            raise e

        return Response(response_json, status=response.status_code)

    def get(self, request):
        def get_document_es(doc_id):
            es_client = init_es_client()
            ELK_DOCUMENT_INDEX = config('ES_DETAIL_DOCUMENT')
            resp = es_client.search(index=ELK_DOCUMENT_INDEX, query={"term": {'ID': doc_id}})
            # logger.debug(resp)
            logger.debug("Got {} hits:".format(resp["hits"]["total"]["value"]))
            hits = resp.get("hits", {}).get("hits", [])
            formatted_path = None
            for item in hits:
                source = item.get("_source", {})
                url_es = source.get("s3_url")
                if url_es:
                    parts = url_es.split('/')
                    doc_id = parts[-1].replace('.pdf', '') 
                    formatted_path = f"{doc_id}.pdf"
            # for hit in resp["hits"]["hits"]:
            #     logger.debug(hit["_source"])
            document_data = {}
            if resp["hits"]["total"]["value"]>0:
                document_data = resp["hits"]["hits"][0]['_source']
                if formatted_path:
                    document_data['docx_url'] = generate_presigned_url_doc(formatted_path)
            return document_data
        
        # logger.debug(request.GET)
        # from_search = request.GET.get('from_search', "0")
        # from_luoc_do = request.GET.get('from_luoc_do', "0")
        # doc_type = request.GET.get('doc_type', "upload")
        document_id = request.GET.get('document_id')
        
        if is_valid_uuid(document_id):
            related_query = DocumentRelated.objects.filter(pk=document_id)
            if related_query.exists():
                logger.debug("1")
                logger.debug('Replace document id with elasticsearch id')
                doc_related = related_query.first()
                logger.debug(f"Search detail ES for document {doc_related.ID}")
                document_data = get_document_es(doc_related.ID)
            else:
                logger.debug("2")
                document = Document.objects.get(id=document_id)
                document_data = DocumentSerializer(document).data
                if document.types == BY_USER:
                    # if document.is_convert:
                    #     document_data['docx_url'] = generate_presigned_url(document.convert)
                    # else:
                        document_data['docx_url'] = generate_presigned_url(document.origin)
                else:
                    if document.origin:
                        document_data['docx_url'] = generate_presigned_url_doc(document.origin)
            # elif doc_type == 'search':

        else:
            logger.debug("4")
            document_data = get_document_es(document_id)

        return Response(document_data, status=status.HTTP_200_OK)
    
    def put(self, request, document_id):
        data = request.data.copy()

        # Normalize giá trị loai_van_ban từ label → value (ví dụ "Nghị Định" → "Nghị định")
        input_label = data.get('loai_van_ban')
        if input_label:
            choices = Document._meta.get_field('loai_van_ban').choices
            for value, label in choices:
                if input_label.strip().lower() == label.strip().lower():
                    data['loai_van_ban'] = value  # ghi đè lại value đúng
                    break

        document = Document.objects.get(id=document_id)
        serializer = DocumentSerializer(document, data=data)
        if serializer.is_valid():
            serializer.save()
            return Response({
                'status': 'success',
                'message': 'Cập nhật tài liệu thành công'
            }, status=status.HTTP_200_OK)
        else:
            error_details = {
                field: [str(error) for error in errors]
                for field, errors in serializer.errors.items()
            }
            return Response({
                'status': 'failed',
                'message': 'Cập nhật tài liệu thất bại',
                'errors': error_details
            }, status=status.HTTP_400_BAD_REQUEST)


class LegalSearchSuggesterView(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request):
        es_client = init_es_client()
        ELK_DOCUMENT_INDEX = config('ELK_DOCUMENT_INDEX')
        # suggest = {
        #     "text" : request.query_params.get('suggest'),
        #     "suggest-completion" : {
        #         "completion" : {
        #             "field" : "title_suggest",
        #             "fuzzy": True
        #         }
        #     }
        # }
        # # logger.debug(suggest)
        # resp = es_client.search(index=ELK_DOCUMENT_INDEX, source=False, suggest=suggest)
        # # logger.debug(resp)
        # suggestions = []
        # for suggest in resp['suggest']['suggest-completion'][0]['options']:
        #     suggestions.append(suggest['text'])
        suggest = {
            "text" : request.query_params.get('suggest'),
            "suggest-completion" : {
                "completion" : {
                    "field" : "title_suggest",
                    "skip_duplicates": True
                }
            }
        }
        # logger.debug(suggest)
        source = {
            "includes": [
                "title"
            ]
        }
        resp = es_client.search(index=ELK_DOCUMENT_INDEX, suggest=suggest, source=source)
        # logger.debug(resp)
        suggestions = []
        for suggest in resp['suggest']['suggest-completion'][0]['options']:
            suggestions.append(suggest['_source']['title'])

        return Response(suggestions, status=status.HTTP_200_OK)

class LegalSearchHistoryView(APIView):
    permission_classes = [IsAuthenticated]
    serializer_class = LegalSearchHistorySerializer

    def get(self, request, *args, **kwargs):
        user = self.request.user
        data = []
        if user.is_superuser:
            query_set = LegalSearchHistory.objects.filter(deleted_time__isnull=True).order_by('-id')
        else:
            query_set = LegalSearchHistory.objects.filter(deleted_time__isnull=True, requested_by=user).order_by('-id')
        for file in query_set:
            data.append(self.serializer_class(file).data)
        return Response(data, status=status.HTTP_200_OK)


class LegalSearchSummaryView(APIView):
    permission_classes = [IsAuthenticated]
    serializer_class = LegalSearchHistorySerializer

    def get(self, request, *args, **kwargs):
        user = self.request.user
        if user.is_superuser:
            query_set = LegalSearchHistory.objects.filter(deleted_time__isnull=True)
        else:
            query_set = LegalSearchHistory.objects.filter(deleted_time__isnull=True, requested_by=user)
        summary = query_set.values('requested_by') \
            .annotate(response_time_avg=Coalesce(Avg('response_time'), 0.0), num_requests=Count('requested_by'))
        return Response(summary, status=status.HTTP_200_OK)


class HistorySearchResultView(APIView):
    permission_classes = [IsAuthenticated]  # Yêu cầu user đăng nhập

    def get(self, request):
        history = HistorySearchResult.objects.filter(user=request.user)  # Chỉ lấy lịch sử của user hiện tại
        serializer = HistorySearchResultSerializer(history, many=True)
        return Response(serializer.data, status=status.HTTP_200_OK)

    def post(self, request):
        user_id = request.data.get("user_id")  # Lấy user_id từ request
        try:
            user = User.objects.get(id=user_id)  # Tìm user trong database
        except User.DoesNotExist:
            return Response({"error": "User không tồn tại"}, status=status.HTTP_400_BAD_REQUEST)

        serializer = HistorySearchResultSerializer(data=request.data)
        if serializer.is_valid():
            serializer.save(user=user)  # Gán user lấy từ ID
            return Response(serializer.data, status=status.HTTP_201_CREATED)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def search(self, request):
        query = request.query_params.get("q", "").strip().lower()  # Lấy từ khóa từ query params
        if not query:
            return Response({"error": "Vui lòng nhập từ khóa tìm kiếm"}, status=status.HTTP_400_BAD_REQUEST)

        history = HistorySearchResult.objects.filter(
            Q(search_params__icontains=query),  # Kiểm tra từ khóa trong search_params
            user=request.user  # Chỉ tìm kiếm trong lịch sử của user hiện tại
        )

        serializer = HistorySearchResultSerializer(history, many=True)
        return Response(serializer.data, status=status.HTTP_200_OK)
    
class HistorySearchResultDetailView(APIView):
    permission_classes = [IsAuthenticated]

    def delete(self, request, history_id):
        """Xóa một bản ghi lịch sử tìm kiếm"""
        try:
            history = HistorySearchResult.objects.get(id=history_id, user=request.user)
            history.delete()
            return Response({"message": "Xóa thành công"}, status=status.HTTP_204_NO_CONTENT)
        except HistorySearchResult.DoesNotExist:
            return Response({"error": "Không tìm thấy lịch sử tìm kiếm"}, status=status.HTTP_404_NOT_FOUND)
        
class HistorySearchResultViewSet(ViewSet):
    permission_classes = [IsAuthenticated]

    def search(self, request):
        """Hàm tìm kiếm lịch sử theo từ khóa"""
        query = request.query_params.get("keyword", "").strip().lower()
        print(f"que di :{query}")
        if not query:
            return Response({"error": "Vui lòng nhập từ khóa tìm kiếm"}, status=400)

        history = HistorySearchResult.objects.filter(
            Q(params__icontains=query),  
            user=request.user
        )

        serializer = HistorySearchResultSerializer(history, many=True)
        return Response(serializer.data, status=200)