import uuid
from django.shortcuts import get_object_or_404
from rest_framework import viewsets
from rest_framework import status
from cls_backend.models import Document, DocumentSummarize
from rest_framework.response import Response

from cls_backend.modules.notification.event import EVENT_IE_DOING
from cls_backend.modules.notification.utils import send_notifications
from .serializers import DocumentSummarizeSerializer
from rest_framework.permissions import IsAuthenticated, AllowAny

class DocumentSummarizeViewSet(viewsets.ModelViewSet):
    queryset = DocumentSummarize.objects.all().order_by('-created_at')
    serializer_class = DocumentSummarizeSerializer
    permission_classes = [AllowAny]

    def get_queryset(self):
        document_id = self.request.query_params.get("document")
        if document_id:
            return self.queryset.filter(document__id=document_id)
        return self.queryset.none()  # Tránh trả về toàn bộ nếu không có id

    def create(self, request, *args, **kwargs):
        document_id = request.data.get("document")

        if not document_id:
            return Response({"error": "Missing document id"}, status=status.HTTP_400_BAD_REQUEST)

        document = get_object_or_404(Document, id=document_id)

        summarize_text = request.data.get("summarize")

        if DocumentSummarize.objects.filter(document=document).exists():
            doc_sum, _ = DocumentSummarize.objects.update_or_create(
                document=document,
                defaults={"summarize": summarize_text},
            )
        else:
            doc_sum = DocumentSummarize.objects.create(
                document=document,
                summarize=summarize_text,
            )

        serialized_data = DocumentSummarizeSerializer(doc_sum).data
        summarize_data = serialized_data.copy()
        if isinstance(summarize_data.get("document"), uuid.UUID):
            summarize_data["document"] = str(summarize_data["document"])

        send_notifications(
            user_id=document.created_by_id,
            event=EVENT_IE_DOING,
            data={
                "name": document.name,
                "status": EVENT_IE_DOING,
                "document_id": str(document.id),
                "summarize": summarize_data,  # gửi toàn bộ DocumentSummarize
            }
        )

        return Response({"message": "Document summarize saved"}, status=status.HTTP_200_OK)


