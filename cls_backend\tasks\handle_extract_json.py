import os
import shutil
import json
from urllib.parse import urlparse
from uuid import UUID
from django.conf import settings
import requests
from cls_backend.models import Document
from cls_backend.utils.docx.convert_doc_to_json import doc_instance
import logging

from cls_backend.utils.file_utils import generate_presigned_url, generate_presigned_url_doc

logger = logging.getLogger("cls")

def handle_extract_json(document):
    logger.debug("Start extract json from docx")
    try:
        # L<PERSON>y thông tin cơ bản
        document_id = str(document.id)  # Chuyển về string
        file_name = os.path.basename(document.name)
        upload_path = os.path.join(settings.PRIVATE_MEDIA_ROOT, 'ocr', 'upload', document_id)

        # Tạo thư mục nếu chưa tồn tại
        os.makedirs(upload_path, exist_ok=True)

        # Xác đ<PERSON>nh định dạng file
        base, extension = os.path.splitext(file_name)
        docx_path = f"{base}.docx" if extension.lower() != '.doc' else f"{base}.doc"

        # Đường dẫn file
        upload_file_path = os.path.join(upload_path, docx_path)

        # Ghi file vào đường dẫn tạm
        if document.is_convert:
            logger.debug("Document is converted")
            with document.convert.open('rb') as origin_file:
                with open(upload_file_path, 'wb') as f:
                    f.write(origin_file.read())
        else:
            logger.debug("Document is not converted")
            with document.origin.open('rb') as origin_file:
                with open(upload_file_path, 'wb') as f:
                    f.write(origin_file.read())

        # Chuyển đổi thành JSON
        json_data = doc_instance.file_to_json(file_path=upload_file_path, file_id=document.id)

        # ✅ Chuyển đổi UUID sang string trước khi lưu
        def uuid_to_str(obj):
            if isinstance(obj, UUID):
                return str(obj)
            raise TypeError(f"Object of type {obj.__class__.__name__} is not JSON serializable")
        
        # Serialize lại toàn bộ JSON với UUID đã chuyển đổi
        json_data = json.loads(json.dumps(json_data, default=uuid_to_str))

        # Lưu lại vào document
        document.json_data = json_data
        document.convert_json = True
        document.save(update_fields=['json_data', 'convert_json'])

        # Xóa thư mục tạm
        shutil.rmtree(upload_path)

    except Exception as e:
        logger.exception(e)
        raise e

def uuid_to_str(obj):
    """ Hàm chuyển UUID sang string để JSON serializable """
    if isinstance(obj, UUID):
        return str(obj)
    raise TypeError(f"Object of type {obj.__class__.__name__} is not JSON serializable")


def handle_extract_json_from_url(url, document_id):
    try:
        # 1️⃣ Lấy file từ URL
        parsed_url = urlparse(url)
        filename = os.path.basename(parsed_url.path)
        presigned_url = generate_presigned_url_doc(filename)
        response = requests.get(presigned_url, stream=True)

        if response.status_code != 200:
            raise Exception(f"Không thể tải file từ URL: {presigned_url}")

        # 2️⃣ Tạo thư mục tạm để lưu file
        upload_path = os.path.join(settings.PRIVATE_MEDIA_ROOT, 'ocr', 'upload', str(document_id))
        if not os.path.exists(upload_path):
            os.makedirs(upload_path)

        # 3️⃣ Lấy tên file từ URL (bỏ query string đi)
        parsed_presigned_url = urlparse(presigned_url)
        file_name = os.path.basename(parsed_presigned_url.path)  # Ví dụ: 22843.pdf
        upload_file_path = os.path.join(upload_path, file_name)

        # 4️⃣ Ghi nội dung vào file
        with open(upload_file_path, 'wb') as f:
            shutil.copyfileobj(response.raw, f)

        # 5️⃣ Chuyển đổi sang JSON
        json_data = doc_instance.file_to_json(file_path=upload_file_path, file_id=document_id)

        # 🔄 Chuyển UUID sang string nếu có trong JSON
        json_data = json.loads(json.dumps(json_data, default=uuid_to_str))

        # 6️⃣ Cập nhật thông tin vào Document
        document = Document.objects.get(pk=document_id)
        document.json_data = json_data
        document.convert_json = True
        document.save(update_fields=['json_data', 'convert_json'])

        # 7️⃣ Xóa thư mục tạm sau khi hoàn tất
        shutil.rmtree(upload_path)

        logger.debug(f"Lưu thành công JSON data cho document {document_id}")
        return json_data

    except Exception as e:
        logger.debug(f"Lỗi khi xử lý văn bản từ URL: {e}")
        return None
