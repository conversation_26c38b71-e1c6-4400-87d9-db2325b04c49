from rest_framework import serializers

from cls_backend.models import User


class UserSerializer(serializers.ModelSerializer):
    password = serializers.CharField(write_only=True)
    # token = serializers.ReadOnlyField()

    class Meta:
        model = User
        fields = ['id', 'email', 'password', 'avatar', 'fullname', 'is_staff']

    def create(self, validated_data):
        return User.objects.create_user(**validated_data)

    def to_representation(self, instance):
        data = super().to_representation(instance)
        if data['is_staff']:
            data['role'] = 'ADMIN'
        else:
            data['role'] = 'USER'
        data.pop('is_staff')
        return data


class ChangePasswordSerializer(serializers.Serializer):
    model = User

    """
    Serializer for password change endpoint.
    """
    current_password = serializers.CharField(required=True)
    new_password = serializers.CharField(required=True)
    
class PasswordResetSerializer(serializers.Serializer):
    email = serializers.EmailField(required=True)
    
class PasswordResetConfirmSerializer(serializers.Serializer):
    new_password = serializers.CharField(min_length=6, required=True)
