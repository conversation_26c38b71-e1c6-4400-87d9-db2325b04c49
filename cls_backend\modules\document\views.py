import json

from django.http import HttpResponse, FileResponse, Http404
import requests
from decouple import config
from django.db.models import Q
from django.shortcuts import get_object_or_404
from rest_framework import status
from rest_framework.decorators import action
from rest_framework.generics import RetrieveAP<PERSON>View, CreateAPIView, ListAPIView
from rest_framework.permissions import IsAuthenticated, AllowAny
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework.viewsets import GenericViewSet
from datetime import datetime
from cls_backend.constants import REQUEST_TIME_OUT
from cls_backend.constants import STATUS_FAILED, STATUS_PENDING, STATUS_PROCESSING, STATUS_SUCCESS
from cls_backend.modules.document.serializers import *
from cls_backend.modules.ocr.serializers import DocumentSerializer
# from cls_backend.tasks.handle_find_document import handle_find_document
import mimetypes
from cls_backend.models import Document
from django.shortcuts import get_object_or_404
from urllib.parse import quote
def get_text_content(json_data):
    return '\n'.join(line['textValue'] for line in json_data['document'])


class DocumentLegalSearchView(APIView):
    permission_classes = [IsAuthenticated]

    def post(self, request):
        data = json.loads(request.body)
        try:
            url = config('CLS_HOST') + '/legal_search/similar_norms/by_query_and_specified_docs'
            headers = {
                'Content-Type': 'application/json'
            }

            response = requests.request("POST", url, headers=headers, data=json.dumps(data), timeout=REQUEST_TIME_OUT)
            return Response(response.json(), status=response.status_code)
        except:
            return Response({}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class DocumentCompareView(APIView):
    permission_classes = [IsAuthenticated]

    # class FormSerializer(serializers.Serializer):
    #     document_old = serializers.IntegerField(
    #         error_messages={
    #             'required': 'document_old: This field is required.'
    #         }
    #     )
    #     document_new = serializers.IntegerField(
    #         error_messages={
    #             'required': 'document_new: This field is required.'
    #         }
    #     )

    def post(self, request):
        filters_serializer = DocumentCompareRequestSerializer(data=request.data)
        filters_serializer.is_valid(raise_exception=True)

        try:
            # document_ids = [filters_serializer.data['document_old'], filters_serializer.data['document_new']]
            # documents = Document.objects.filter(id__in=document_ids).values('id', 'json_data')

            # doc_old = None
            # doc_new = None
            # for document in documents:
            #     if document['id'] == filters_serializer.data['document_old']:
            #         doc_old = document['json_data']
            #     elif document['id'] == filters_serializer.data['document_new']:
            #         doc_new = document['json_data']
            
             # TODO: upgrade this logic to handle case when one of the document is not converted
            # if request.data['document_old'] == request.data['document_new']:
            #     return Response({"error": "Không được so sánh "}, status=status.HTTP_404_NOT_FOUND)
            
            doc_old = Document.objects.get(id=request.data['document_old'])
            doc_new = Document.objects.get(id=request.data['document_new'])

            if not doc_old.json_data:
                return Response({"error": f"Tài liệu {doc_old.name} chưa được xử lý"}, status=status.HTTP_400_BAD_REQUEST)
        
            if not doc_new.json_data:
                return Response({"error": f"Tài liệu {doc_new.name} chưa được xử lý"}, status=status.HTTP_400_BAD_REQUEST)


            old_content = get_text_content(doc_old.json_data)
            new_content = get_text_content(doc_new.json_data)

            return Response({
                'document_old': old_content,
                'document_new': new_content,
            }, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


# class FindDocumentViewSet(RetrieveAPIView, CreateAPIView, ListAPIView, GenericViewSet):
#     permission_classes = [IsAuthenticated]
#     serializer_class = FindDocumentSerializer
#     query_set = Document.objects.filter(deleted_at=None)

#     # def get_queryset(self):
#     #     request = self.request
#     #     user = self.request.user
#     #     keyword = request.query_params.get('keyword')
#     #     query_set = self.query_set
#     #     if not user.is_superuser:
#     #         query_set = query_set.filter(created_by=user)
#     #     if keyword and len(keyword) > 0:
#     #         query_set = query_set.filter(name__icontains=keyword)
        
#     #     query_set = query_set.exclude(
#     #         # Q(is_convert=True) 
#     #         # & 
#     #         Q(status__in=[
#     #             STATUS_FAILED,
#     #             STATUS_PROCESSING,
#     #             STATUS_PENDING
#     #         ])
#     #     )
        
#     #     query_set = query_set.defer('json_data')
#     #     return query_set.order_by('-id')

#     # def create(self, request, *args, **kwargs):
#     #     data = request.data
#     #     file_serializer = self.serializer_class(data=data)
#     #     file_serializer.is_valid(raise_exception=True)

#     #     document_data = {
#     #         'created_by': request.user, 
#     #         'name': data.get('origin').name, 
#     #         'compare_status': STATUS_PENDING
#     #     }
#     #     if data.get('origin').name.endswith('.docx'):
#     #         document_data['status'] = STATUS_SUCCESS

#     #     file_serializer.save(**document_data)
#     #     handle_find_document.delay(document_id=file_serializer.data['id'])
#     #     return Response(file_serializer.data, status=status.HTTP_201_CREATED)

#     @action(methods=['post'], detail=True)
#     def rerun(self, request, *args, **kwargs):
#         document_id = self.kwargs['pk']
#         filters = {'id': document_id, 'deleted_at': None}

#         if not self.request.user.is_superuser:
#             filters['created_by'] = self.request.user

#         document = get_object_or_404(Document, **filters)
#         document.compare_status = STATUS_PENDING
#         document.save(update_fields=['compare_status'])

#         # handle_find_document.delay(document_id=document.id)

#         return Response(status=status.HTTP_200_OK)


class FindDocumentHistoryViewSet(RetrieveAPIView, CreateAPIView, ListAPIView, GenericViewSet):
    permission_classes = [IsAuthenticated]
    serializer_class = DocumentCompareLessInfoSerializer

    def get_queryset(self):
        request = self.request
        user = request.user
        keyword = request.query_params.get('keyword')
        query_set = DocumentCompare.objects.select_related('document') \
            .only('document__name', 'document__origin', 'document__id', 'document__created_by',
                  'document__compare_status') \
            .filter(deleted_at=None)

        if not user.is_superuser:
            query_set = query_set.filter(document__created_by=user)
        if keyword and len(keyword) > 0:
            query_set = query_set.filter(document__name__icontains=keyword)
        query_set = query_set.defer('document_based', 'document_related', 'dieu_khoan')
        return query_set.order_by('-id')

    def retrieve(self, request, *args, **kwargs):
        document_id = self.kwargs['pk']
        filters = {'document__id': document_id, 'document__deleted_at': None}

        if not self.request.user.is_superuser:
            filters['document__created_by'] = self.request.user

        document_compare = get_object_or_404(DocumentCompare, **filters)
        serializer = RetrieveFindDocumentSerializer(document_compare)

        return Response(serializer.data, status=status.HTTP_200_OK)


class CheckDocumentExistsViewSet(RetrieveAPIView, GenericViewSet):
    permission_classes = []
    serializer_class = DocumentCompareLessInfoSerializer


    def retrieve(self, request, *args, **kwargs):
        document_id = self.kwargs['pk']
        if Document.objects.filter(id=document_id).exists():
            return Response(True, status=status.HTTP_200_OK)
        else:
            return Response(False, status=status.HTTP_200_OK)
class UpdateDocumentName(APIView):
    permission_classes = [IsAuthenticated]  # Chỉ cho phép người dùng đã đăng nhập

    def put(self, request, pk):
        document = get_object_or_404(Document, pk=pk)
        new_name = request.data.get("name")

        if not new_name:
            return Response({"error": "Tên tài liệu không được trống"}, status=status.HTTP_400_BAD_REQUEST)

        document.name = new_name
        document.save()

        return Response({"message": "Cập nhật tên tài liệu thành công", "name": document.name}, status=status.HTTP_200_OK)

class IEVBHC(APIView):
    permission_classes = [IsAuthenticated]  # Chỉ cho phép người dùng đã đăng nhập

    def post(self, request, *args, **kwargs):
        document_id = request.data.get('document_id')
        if not document_id:
            return Response({"error": "Missing document_id"}, status=status.HTTP_400_BAD_REQUEST)

        # Tìm Document theo ID
        document = get_object_or_404(Document, id=document_id)

        # Kiểm tra file origin có tồn tại không
        if not document.origin :
            return Response({"error": "Document has no origin file or file is missing."}, status=status.HTTP_400_BAD_REQUEST)

        try:
            with document.origin.open('rb') as f:
                files = {'file': (os.path.basename(document.origin.name), f)}
                data = {
                    'return_type': 'admin',
                    'return_now': '1',
                    'extract_formula': '0',
                    'extract_signature': '0',
                    'extract_checkbox': '0',
                    'extract_figure':'0',
                    'is_full_line':'1'
                }
                url = config('IE_VBHC_URL')
                response = requests.post(url, files=files, data=data)
                response.raise_for_status()
                data = response.json()
                document.co_quan_ban_hanh = data.get("admin", {}).get("SenderCode", [{}])[0].get("content", "")
                source_doc_no = (data.get("admin", {}).get("SourceDocNo", [{}])[0].get("content") or "")
                notation = (data.get("admin", {}).get("Notation", [{}])[0].get("content") or "")
                if source_doc_no and notation:
                    if source_doc_no.endswith("/") or notation.startswith("/"):
                        document.so_hieu = source_doc_no + notation
                    else:
                        document.so_hieu = source_doc_no + "/" + notation
                else:
                    document.so_hieu = source_doc_no or notation
                # document.so_hieu = data.get("admin", {}).get("SourceDocNo", [{}])[0].get("content", "") + "/" + data.get("admin", {}).get("Notation", [{}])[0].get("content", "")
                document.ngay_ban_hanh = data.get("DocDate", [{}])[0].get("content", "")
                document.trich_yeu = data.get("admin", {}).get("Brief", [{}])[0].get("content", "")
                document.nguoi_ky = data.get("Signer", [{}])[0].get("content", "")
                document.loai_van_ban = data.get("admin", {}).get("DocType", [{}])[0].get("content", "")
                document.save()
                serialized_doc = DocumentSerializer(document)
                return Response(serialized_doc.data, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
    
def download_file(request):
    doc_id = request.GET.get('doc_id')
    document = get_object_or_404(Document, pk=doc_id)
    type = request.GET.get('type', '')
    file_type = document.name.split('.')[-1]
    # Đọc dữ liệu từ file gốc (origin)
    try:
        if type == file_type :
            with document.origin.open('rb') as file:
                file_data = file.read()  # Đọc toàn bộ dữ liệu từ file

            if not file_data:
                return HttpResponse("File not found", status=404)

            # Xác định kiểu nội dung (mimetype) của file
            content_type, _ = mimetypes.guess_type(document.origin.name)
            if not content_type:
                content_type = 'application/octet-stream'

            # Tạo HttpResponse và trả về file dưới dạng dữ liệu nhị phân
            response = HttpResponse(file_data, content_type=content_type)
            response['Content-Disposition'] = f'attachment; filename="{document.name}"'

            return response
        elif type != file_type and file_type == 'docx':
            return HttpResponse(f"Không thể tải file pdf", status=404)
        else:
            with document.convert.open('rb') as file:
                file_data = file.read()  # Đọc toàn bộ dữ liệu từ file

            if not file_data:
                return HttpResponse("File not found", status=404)

            # Xác định kiểu nội dung (mimetype) của file
            content_type, _ = mimetypes.guess_type(document.convert.name)
            if not content_type:
                content_type = 'application/octet-stream'

            # Tạo HttpResponse và trả về file dưới dạng dữ liệu nhị phân
            response = HttpResponse(file_data, content_type=content_type)
            response['Content-Disposition'] = f'attachment; filename="{document.name}"'

            return response

    except Exception as e:
        return HttpResponse(f"Error: {str(e)}", status=500)

class DocumentBinaryAPIView(APIView):
    permission_classes = [AllowAny]

    def get(self, request, pk):
        document = get_object_or_404(Document, pk=pk)

        if not document.origin and not document.convert:
            raise Http404("Không tìm thấy file gốc hoặc file chuyển đổi.")

        try:
            # Nếu origin là .docx thì dùng origin
            if document.origin and document.origin.name.lower().endswith(('.doc', '.docx')):
                file_to_send = document.origin
            # Ngược lại, dùng file convert
            elif document.convert:
                file_to_send = document.convert
            else:
                raise Http404("Không có file phù hợp để tải xuống.")

            file_handle = file_to_send.open('rb')
            content_type, _ = mimetypes.guess_type(file_to_send.name)
            if not content_type:
                content_type = 'application/octet-stream'

            response = FileResponse(file_handle, content_type=content_type)
            filename = document.name or file_to_send.name
            response['Content-Disposition'] = f"attachment; filename*=UTF-8''{quote(filename)}"
            return response

        except Exception as e:
            raise Http404(f"Lỗi khi đọc file: {str(e)}")
