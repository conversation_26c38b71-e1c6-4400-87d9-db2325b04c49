from rest_framework import serializers

from cls_backend.models import *
from cls_backend.utils.file_validator import FileValida<PERSON>
from cls_backend.utils.file_utils import generate_presigned_url

UPLOAD_MAX_SIZE = 200 * 1024 * 1024  # 200 MB in bytes
validate_file = FileValidator(max_size=UPLOAD_MAX_SIZE,
                              content_types=[
                                  'application/pdf',  # pdf
                                  'application/vnd.openxmlformats-officedocument.wordprocessingml.document',  # docx
                                  'application/msword',  # doc
                              ])


class DocumentCompareSerializer(serializers.ModelSerializer):
    updated_at = serializers.DateTimeField(format="%Y-%m-%d %H:%M:%S", read_only=True)

    class Meta:
        model = DocumentCompare
        fields = ["id", "document_based", "document_related", "dieu_khoan", "updated_at"]


class DocumentCompareLessInfoSerializer(serializers.ModelSerializer):
    id = serializers.UUIDField(source='document.id')
    name = serializers.CharField(source='document.name')
    origin = serializers.FileField(source='document.origin')
    updated_at = serializers.DateTimeField(format="%Y-%m-%d %H:%M:%S", read_only=True)
    status = serializers.CharField(source="document.compare_status", read_only=True)
    compare_status_display = serializers.CharField(source="document.get_compare_status_display", read_only=True)

    class Meta:
        model = DocumentCompare
        fields = ["id", "name", "updated_at", 'origin', 'status', 'compare_status_display']
        read_only_fields = ["id", "name", "updated_at", 'origin', 'status']


class FindDocumentSerializer(serializers.ModelSerializer):
    origin = serializers.FileField(validators=[validate_file], required=True)
    status = serializers.CharField(source="compare_status", read_only=True)
    compare_status_display = serializers.CharField(source='get_compare_status_display', read_only=True)
    created_at = serializers.DateTimeField(format="%Y-%m-%d %H:%M:%S", read_only=True)
    name = serializers.CharField(read_only=True)

    class Meta:
        model = Document
        fields = ['id', 'origin', 'status', 'compare_status_display', 'created_at', 'name']
        read_only_fields = ['id', 'origin', 'status', 'created_at', 'name']


class RetrieveFindDocumentSerializer(serializers.ModelSerializer):
    name = serializers.CharField(source='document.name', read_only=True)
    updated_at = serializers.DateTimeField(format="%Y-%m-%d %H:%M:%S", read_only=True)
    # origin = serializers.FileField(source='document.origin', read_only=True)
    origin = serializers.SerializerMethodField(read_only=True)

    def get_origin(self, obj):
        # print(obj.document.origin)
        return generate_presigned_url(obj.document.origin)

    class Meta:
        model = DocumentCompare
        fields = ['id', 'origin', 'updated_at', 'name', 'document_based', 'document_related', 'dieu_khoan']
        read_only_fields = ['id', 'origin', 'updated_at', 'name', 'document_based', 'document_related', 'dieu_khoan']

class DocumentCompareRequestSerializer(serializers.Serializer):
    document_old = serializers.UUIDField()
    document_new = serializers.UUIDField()